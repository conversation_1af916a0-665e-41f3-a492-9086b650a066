extends Control
class_name ReversedMessagePuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var message_label: Label = $PuzzlePanel/VBoxContainer/MessageLabel
@onready var note_label: RichTextLabel = $PuzzlePanel/VBoxContainer/NoteLabel
@onready var answer_field: LineEdit = $PuzzlePanel/VBoxContainer/AnswerContainer/AnswerField
@onready var submit_button: TextureButton = $PuzzlePanel/VBoxContainer/AnswerContainer/SubmitButton
@onready var hint_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var close_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton

var virtual_keyboard: VirtualKeyboard

var correct_answer: String = "POD VONKAJŠÍMI MÚRMI SCHOVÁ VCHOD"
var hint_level: int = 0
var max_hints: int = 3

func _ready():
	hide()

	# Vytvorenie virtuálnej klávesnice
	create_virtual_keyboard()

	# Pripojenie signálov
	print("🔗 ReversedMessagePuzzle: Connecting signals...")
	print("🔗 submit_button exists: ", submit_button != null)
	print("🔗 hint_button exists: ", hint_button != null)
	print("🔗 close_button exists: ", close_button != null)

	if submit_button:
		submit_button.pressed.connect(_on_submit_pressed)
		print("🔗 Submit button connected")
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
		print("🔗 Hint button connected")
	if close_button:
		close_button.pressed.connect(_on_close_pressed)
		print("🔗 Close button connected")
	if answer_field:
		answer_field.text_submitted.connect(_on_text_submitted)
		answer_field.focus_entered.connect(_on_input_focus_entered)
		answer_field.focus_exited.connect(_on_input_focus_exited)
		print("🔗 Answer field connected")

	setup_puzzle()

func setup_puzzle():
	if title_label:
		title_label.text = "Van Helsingov denník"
	
	if description_label:
		description_label.text = "[center]Na stole nájdete Van Helsingov denník.[/center]\n\nPosledná stránka obsahuje zvláštnu správu:"
	
	if message_label:
		message_label.text = "DOP IMÍŠJAKNOV IMARÚM ÁVOHCS DOHCV"
		message_label.add_theme_font_size_override("font_size", 18)
		message_label.add_theme_color_override("font_color", Color(0.8, 0.8, 0.2))  # Zlatá farba
	
	if note_label:
		note_label.text = "[center][i]Van Helsingova poznámka:[/i]\n\"V nebezpečenstve píšem každé slovo odzadu.\"[/center]"

func show_puzzle():
	show()
	# Uložiť stav puzzle
	GameManager.set_game_state_puzzle("ReversedMessagePuzzle")
	if answer_field:
		answer_field.grab_focus()
	# Zobraziť virtuálnu klávesnicu
	print("🔤 ReversedMessagePuzzle: show_puzzle - virtual_keyboard existuje: ", virtual_keyboard != null)
	if virtual_keyboard:
		print("🔤 ReversedMessagePuzzle: Zobrazujem virtuálnu klávesnicu...")
		virtual_keyboard.show_keyboard(answer_field)
	else:
		print("❌ ReversedMessagePuzzle: virtual_keyboard je null!")

func _on_submit_pressed():
	check_answer()

func _on_text_submitted(text: String):
	check_answer()

func check_answer():
	if not answer_field:
		return
	
	var user_answer = normalize_text(answer_field.text)
	var correct_normalized = normalize_text(correct_answer)
	
	if user_answer == correct_normalized:
		# Správna odpoveď
		AudioManager.play_puzzle_success_sound()
		puzzle_solved.emit()
		hide()
	else:
		# Nesprávna odpoveď
		AudioManager.play_puzzle_error_sound()
		show_error_feedback()

func normalize_text(text: String) -> String:
	# Odstránenie diakritiky a normalizácia
	var normalized = text.to_upper().strip_edges()
	
	# Základná náhrada diakritiky
	var replacements = {
		"Á": "A", "Ä": "A", "Č": "C", "Ď": "D", "É": "E", "Ě": "E",
		"Í": "I", "Ľ": "L", "Ĺ": "L", "Ň": "N", "Ó": "O", "Ô": "O",
		"Ŕ": "R", "Š": "S", "Ť": "T", "Ú": "U", "Ů": "U", "Ý": "Y",
		"Ž": "Z"
	}
	
	for old_char in replacements:
		normalized = normalized.replace(old_char, replacements[old_char])
	
	return normalized

func show_error_feedback():
	if answer_field:
		answer_field.modulate = Color.RED
		var tween = create_tween()
		tween.tween_property(answer_field, "modulate", Color.WHITE, 0.5)

func _on_hint_pressed():
	print("🔍 ReversedMessagePuzzle: Hint button pressed!")
	hint_level += 1

	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		print("🔍 ReversedMessagePuzzle: Showing hint: ", hint_text)
		show_hint_dialog(hint_text)
	else:
		print("🔍 ReversedMessagePuzzle: All hints used")
		show_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "Van Helsing píše, že píše slová odzadu. Čo to znamená?"
		2:
			return "Skúste čítať každé slovo pozpätku."
		3:
			return "DOP = POD, IMÍŠJAKNOV = VONKAJŠÍMI..."
		_:
			return "Už ste použili všetky nápovedy!"

func show_hint_dialog(hint_text: String):
	print("💬 ReversedMessagePuzzle: Creating hint dialog with text: ", hint_text)

	# Vytvoríme jednoduchý popup panel
	var popup = PopupPanel.new()
	add_child(popup)

	# Nastavíme veľkosť a pozíciu
	popup.size = Vector2(400, 200)
	popup.position = Vector2(160, 300)  # Centrované pre 720x1280

	# Vytvoríme VBoxContainer pre obsah
	var vbox = VBoxContainer.new()
	popup.add_child(vbox)
	vbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	vbox.add_theme_constant_override("separation", 10)

	# Pridáme margin
	var margin = MarginContainer.new()
	vbox.add_child(margin)
	margin.add_theme_constant_override("margin_left", 20)
	margin.add_theme_constant_override("margin_right", 20)
	margin.add_theme_constant_override("margin_top", 20)
	margin.add_theme_constant_override("margin_bottom", 20)

	# Pridáme titulok
	var title_label = Label.new()
	margin.add_child(title_label)
	title_label.text = "Nápoveda"
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 18)

	# Pridáme text nápovedy
	var hint_label = Label.new()
	margin.add_child(hint_label)
	hint_label.text = hint_text
	hint_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	hint_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	hint_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER

	# Pridáme tlačidlo OK
	var ok_button = Button.new()
	margin.add_child(ok_button)
	ok_button.text = "OK"
	ok_button.custom_minimum_size = Vector2(100, 40)

	# Pripojíme signál na zatvorenie
	ok_button.pressed.connect(func():
		print("💬 ReversedMessagePuzzle: OK button pressed")
		popup.queue_free()
	)

	print("💬 ReversedMessagePuzzle: Showing popup...")
	popup.popup()

func create_virtual_keyboard():
	"""Vytvorí a pripojí virtuálnu klávesnicu"""
	print("🔤 ReversedMessagePuzzle: Vytváram virtuálnu klávesnicu...")

	var keyboard_scene = preload("res://scenes/VirtualKeyboard.tscn")
	if keyboard_scene == null:
		print("❌ ReversedMessagePuzzle: Chyba - VirtualKeyboard.tscn sa nepodarilo načítať!")
		return

	virtual_keyboard = keyboard_scene.instantiate()
	if virtual_keyboard == null:
		print("❌ ReversedMessagePuzzle: Chyba - VirtualKeyboard sa nepodarilo vytvoriť!")
		return

	add_child(virtual_keyboard)

	# Pripojenie signálov klávesnice
	virtual_keyboard.key_pressed.connect(_on_virtual_key_pressed)
	virtual_keyboard.backspace_pressed.connect(_on_virtual_backspace_pressed)
	virtual_keyboard.space_pressed.connect(_on_virtual_space_pressed)
	virtual_keyboard.enter_pressed.connect(_on_virtual_enter_pressed)

	print("🔤 ReversedMessagePuzzle: Virtuálna klávesnica vytvorená a pripojená")

func _on_virtual_key_pressed(letter: String):
	"""Spracuje stlačenie písmena na virtuálnej klávesnici"""
	if answer_field:
		answer_field.text += letter

func _on_virtual_backspace_pressed():
	"""Spracuje stlačenie backspace na virtuálnej klávesnici"""
	if answer_field and answer_field.text.length() > 0:
		answer_field.text = answer_field.text.substr(0, answer_field.text.length() - 1)

func _on_virtual_space_pressed():
	"""Spracuje stlačenie medzery na virtuálnej klávesnici"""
	if answer_field:
		answer_field.text += " "

func _on_virtual_enter_pressed():
	"""Spracuje stlačenie enter na virtuálnej klávesnici"""
	check_answer()

func _on_input_focus_entered():
	"""Zobrazí virtuálnu klávesnicu keď sa aktivuje textové pole"""
	if virtual_keyboard:
		virtual_keyboard.show_keyboard(answer_field)

func _on_input_focus_exited():
	"""Skryje virtuálnu klávesnicu keď sa deaktivuje textové pole"""
	# Neskrývame klávesnicu automaticky, nech zostane zobrazená

func _on_close_pressed():
	print("❌ ReversedMessagePuzzle: Close button pressed!")
	# Skryť virtuálnu klávesnicu
	if virtual_keyboard:
		virtual_keyboard.hide_keyboard()

	# Obnoviť story stav
	GameManager.set_game_state_story()
	puzzle_failed.emit()
	hide()
	print("❌ ReversedMessagePuzzle: Puzzle closed")

func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()
